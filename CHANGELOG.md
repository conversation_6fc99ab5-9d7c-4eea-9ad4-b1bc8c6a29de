# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Changed
- **Comprehensive Redoc Documentation Enhancement** - Complete overhaul of WebSocket streams documentation with detailed specifications for each stream
  - **Professional Branding**: Replaced "SolanaTracker Streams" with "Premium Streams" throughout documentation to hide internal API provider
  - **Complete Stream Specifications**: Added comprehensive details for all 14 WebSocket streams including:
    - **Tier Requirements**: Clear indication of which subscription tier is required (Free, Basic+, Premium+, Enterprise)
    - **Credit Costs**: Exact credit consumption per message for each stream
    - **Parameter Requirements**: Detailed parameter specifications for parameterized streams
    - **Example Responses**: Real JSON response examples for every stream showing actual data structure
  - **Core Streams Documentation**: Enhanced documentation for kol-feed, jupiter-amm-swaps, pumpfun-amm-swaps, jupiter-dca-orders with tier requirements and example responses
  - **Premium Streams Documentation**: Complete specifications for all 10 Premium streams (tokens-launched, tokens-graduating, tokens-graduated, pool-changes, token-transactions, price-updates, wallet-transactions, token-metadata, token-holders, token-changes)
  - **Improved Subscription Format**: Changed from colon-separated format to proper parameters object format for better clarity
  - **Professional Code Blocks**: All subscription examples now use proper JSON code blocks with syntax highlighting
  - **Real Example Data**: Used actual addresses and realistic data from testing for consistency and accuracy
  - **Clean Architecture**: Removed non-existent documentation endpoints and duplicate sections
  - **User-Friendly Layout**: Organized streams by category (Core vs Premium) with clear visual separation
  - **Copy-Paste Ready**: All examples are properly formatted for easy integration into user code
  - **Result**: Professional, comprehensive documentation that provides developers with everything needed to implement WebSocket streaming

### Fixed
- **SolanaTracker Stream Subscription Issue** - Fixed critical bug where WebSocket subscriptions to SolanaTracker streams (tokens-launched, tokens-graduating, etc.) were not triggering the actual SolanaTracker worker connections
  - Added automatic stream subscription handler in StreamManager that listens to Redis `stream_subscription` events
  - When users subscribe to SolanaTracker streams via WebSocket, the system now automatically subscribes the worker to the corresponding SolanaTracker room
  - Maps stream names to SolanaTracker rooms: `tokens-launched` → `latest`, `tokens-graduating` → `graduating`, etc.
  - Handles both subscription and unsubscription events for proper connection lifecycle management
  - Added missing environment variables `SOLANA_TRACKER_WSS_URL` and `SOLANA_TRACKER_WSS_KEY` to `.env.example`
  - **Result**: Enterprise users can now successfully receive real-time token launch data from SolanaTracker API

- **SolanaTracker Stream Cleanup Resource Leak** - Fixed critical issue where SolanaTracker WebSocket connections remained open after users disconnected, causing resource leaks
  - **Enhanced Bulk Unsubscription**: Implemented efficient bulk cleanup when users disconnect from WebSocket, preventing individual unsubscription calls for each stream
  - **Improved Connection Cleanup**: Enhanced connection removal with proper WebSocket event listener cleanup to prevent memory leaks
  - **Graceful Connection Closure**: Added graceful WebSocket closure with fallback to forced termination for robust cleanup
  - **Disconnect Cleanup Tracking**: Implemented tracking system to prevent duplicate cleanup attempts for the same connection
  - **Immediate Cleanup**: Fixed connection pool to immediately close connections when no subscribers remain (removed 500ms delay)
  - **Reconnection Cleanup**: Clear pending reconnection attempts when connections are properly closed
  - **Parameter Mapping Fix**: Corrected `price-updates` stream to use `pool_id` parameter instead of `token` for proper SolanaTracker room mapping
  - **Comprehensive Error Handling**: Added robust error handling for connection cleanup failures with detailed logging
  - **Test Coverage**: Created `tests/test_solana_tracker_cleanup.js` to verify cleanup functionality

### Added
- **Comprehensive SolanaTracker WebSocket Test Suite** - Created extensive testing framework to verify all 10 SolanaTracker streams are working correctly
  - Tests all stream types: tokens-launched, tokens-graduating, tokens-graduated, pool-changes, token-transactions, price-updates, wallet-transactions, token-metadata, token-holders, token-changes
  - Implements intelligent timeout strategies based on expected message frequency (30s-5min)
  - Verifies complete data flow: SolanaTracker API → StreamManager → WebSocket clients
  - Provides detailed debugging and comprehensive reporting for each stream
  - Validates subscription confirmation and real-time data delivery

- **Complete SolanaTracker WebSocket Documentation** - Created comprehensive documentation for all SolanaTracker stream subscriptions
  - Updated `docs/SOLANA_TRACKER_GUIDE.md` with verified subscription formats and real data examples
  - Created `docs/SOLANA_TRACKER_WEBSOCKET_REFERENCE.md` as quick reference for all 10 streams
  - Documented correct subscription format: `{"type": "subscribe", "payload": {"stream": "stream-name"}}`
  - Clarified that colon-separated parameters (e.g., "tokens-graduating:sol:100") are NOT supported at WebSocket level
  - Included real data examples from live testing (AINTERN, dolf, GoMint, Cat On Mask, The Rock tokens)
  - Added stream frequency expectations, error handling, and troubleshooting guides
  - Updated WebSocket guide with SolanaTracker subscription examples

- **Parameterized SolanaTracker Graduating Streams** - Added support for market cap threshold parameters in tokens-graduating stream
  - Implemented parameter support in WebSocket subscription payload: `{"stream": "tokens-graduating", "parameters": {"marketCapThreshold": 175}}`
  - Maps to SolanaTracker room format: `graduating:sol:175` for specific market cap thresholds
  - Updated StreamManager to handle parameterized room mapping
  - Enhanced SolanaTracker worker to support parameterized graduating rooms
  - Updated data transformation to include market cap threshold information in responses
  - Added comprehensive test coverage for parameterized graduating streams
  - Updated all documentation with parameterized subscription examples

- **Critical Memory Leak Fix** - Implemented comprehensive cleanup system to prevent SolanaTracker connection memory leaks
  - **Automatic Unsubscription on Disconnect**: WebSocket disconnections now automatically publish unsubscription events to Redis
  - **SolanaTracker Connection Cleanup**: StreamManager properly handles unsubscription events to clean up SolanaTracker connections
  - **Orphaned Connection Detection**: Added periodic maintenance to detect and clean up connections with no subscribers
  - **Maintenance Scheduler**: Implemented 5-minute periodic cleanup to prevent memory accumulation
  - **Graceful Shutdown Enhancement**: Improved shutdown process to properly clean up all active connections
  - **Database Session Tracking**: Enhanced session management to track disconnections and cleanup
  - **Comprehensive Testing**: Created test suite to verify automatic cleanup on client disconnect scenarios

### Added
- **SolanaTracker Worker Module** - New efficient proxy for SolanaTracker WebSocket API
  - Implemented connection pooling for WebSocket streams to minimize API usage
  - Created modular data transformation functions for easy maintenance and refactoring
  - Added support for all SolanaTracker room types (latest, graduating, graduated, pool, transaction, price, wallet, metadata, holders, token)
  - Integrated with existing StreamManager system following established patterns
  - Added proper error handling, reconnection logic with exponential backoff, and logging
  - Implemented transaction deduplication to prevent duplicate processing
  - **Premium+ tier feature with 0 credit cost** - Included as part of Premium and Enterprise subscriptions
  - **Real-time only** - No historical data storage to optimize performance and reduce complexity
  - Added comprehensive documentation in `docs/SOLANA_TRACKER_GUIDE.md`

### Technical Implementation
- **Connection Pooling System**:
  - Multiple users can subscribe to the same SolanaTracker endpoint without creating duplicate connections
  - Connections are automatically created when needed and closed when no subscribers remain
  - Efficient resource management with subscriber tracking per room
  
- **Modular Architecture**:
  - Discrete transformation functions for each data type (latest, transactions, price updates, etc.)
  - Abstracted data source details for future flexibility
  - Well-named functions that can be easily refactored
  
- **Integration Features**:
  - Follows KolFeed and KafkaStreams worker patterns
  - Uses Redis pub/sub for internal communication
  - Stores historical data in Redis lists (last 100 messages per stream)
  - Integrates with StreamManager's event-driven system
  - Supports dynamic subscription/unsubscription

### Environment Variables
- Added `SOLANA_TRACKER_WSS_URL` - WebSocket URL for SolanaTracker API (wss://datastream.solanatracker.io)
- Added `SOLANA_TRACKER_WSS_KEY` - API key for SolanaTracker authentication

### New Streams Available (Premium+ Tier, 0 Credits)
**Basic Streams:**
- `tokens-launched` - Latest tokens and pools from Solana blockchain
- `tokens-graduating` - Tokens approaching graduation on Pump.fun/Moonshot
- `tokens-graduated` - Recently graduated tokens

**Parameterized Streams:**
- `pool-changes` - Real-time updates for specific pools
- `token-transactions` - Transaction updates for specific tokens
- `price-updates` - Price updates for specific pools/tokens (supports multiple formats)
- `wallet-transactions` - Transaction updates for specific wallets
- `token-metadata` - Token metadata updates (BETA)
- `token-holders` - Token holder count changes (BETA)
- `token-changes` - All updates for specific tokens

### Database Updates
- ✅ **COMPLETED**: Added SolanaTracker stream definitions to `stream_definitions` table
- ✅ **UPDATED**: Changed tier requirement to "premium" (available for Premium and Enterprise tiers only)
- ✅ **CONFIGURED**: Updated `access_tiers` table with proper stream access permissions
- Set credit cost to 0 for all SolanaTracker streams (included feature)
- **10/10 streams successfully added** to production database

### Access Control & Security
- ✅ **IMPLEMENTED**: Comprehensive tier-based access control system
- ✅ **TESTED**: Basic tier users properly restricted from SolanaTracker streams
- ✅ **VERIFIED**: Premium and Enterprise users have full access to all streams
- ✅ **VALIDATED**: Credit system correctly applies 0 credits for Premium+ users

### Documentation Updates
- ✅ **ENHANCED**: Comprehensive Redoc API documentation with detailed SolanaTracker stream information
- ✅ **ADDED**: Complete WebSocket streaming documentation with examples and troubleshooting
- ✅ **DETAILED**: Parameter requirements and formats for all parameterized streams
- ✅ **IMPROVED**: User-friendly explanations and practical usage examples
- Created comprehensive `docs/SOLANA_TRACKER_GUIDE.md` with:
  - Architecture overview and data flow diagrams
  - Configuration instructions and environment variables
  - Usage examples and API integration guide
  - Data transformation examples
  - Troubleshooting and performance considerations
- Updated `docs/README.md` to include SolanaTracker worker information
- Added SolanaTracker streams to WebSocket streams documentation

### Live Testing Results
- **✅ Connection Success**: WebSocket URL format `WSS_URL/API_KEY` working perfectly
- **✅ Data Structure Validation**: 11+ real messages captured, structure confirmed compatible
- **✅ Connection Pooling Verified**: Multiple subscribers (5) sharing single connection
- **✅ Partial Unsubscription**: Connection maintained with remaining subscribers
- **✅ Complete Unsubscription**: Proper connection cleanup when all users leave
- **✅ Multiple Rooms**: Support for latest, graduating, graduated rooms confirmed
- **✅ Real-time Data**: Successfully receiving live token data from Pump.fun, Meteora, Raydium

### Files Modified
- `src/workers/solanaTracker.js` - New worker module (700+ lines), removed historical storage
- `src/websocket/StreamManager.js` - Integrated SolanaTracker worker, updated stream mappings
- `src/docs/openapi.json` - Added comprehensive SolanaTracker documentation and schemas
- `tests/test_solana_tracker_live.js` - Comprehensive live testing suite
- `tests/test_connection_pooling.js` - Connection pooling validation tests
- `tests/test_solana_tracker_access_control.js` - Comprehensive access control testing
- `tests/debug_solana_tracker_connection.js` - Connection debugging utility
- `scripts/update_solana_tracker_tiers.js` - Database tier configuration script
- `scripts/check_and_add_streams.js` - Database stream addition script
- `database/add_solana_tracker_streams.sql` - SQL statements for database stream definitions
- `docs/SOLANA_TRACKER_GUIDE.md` - New comprehensive documentation
- `docs/README.md` - Updated with SolanaTracker information
- `CHANGELOG.md` - This changelog

### Code Quality
- Follows established patterns from existing workers
- Comprehensive error handling and logging
- Modular design for easy maintenance
- Connection pooling for efficient resource usage
- Automatic cleanup and maintenance routines

---

## Previous Versions

*Previous changelog entries would be documented here as the project evolves*
